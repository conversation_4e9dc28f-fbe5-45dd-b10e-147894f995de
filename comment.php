<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغییر کامنت‌های فایل bot.php</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e2f3ff;
            color: #0c5460;
            border: 1px solid #b8daff;
            margin-bottom: 20px;
        }
        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 تغییر کامنت‌های فایل bot.php</h1>
            <p>ابزار تغییر تمام کامنت‌های فایل bot.php به متن ثابت</p>
        </div>
        
        <div class="content">
            <?php
            $botFilePath = 'bot.php';
            $backupFilePath = 'bot_backup_' . date('Y-m-d_H-i-s') . '.php';
            
            // نمایش اطلاعات فایل
            if (file_exists($botFilePath)) {
                $fileSize = filesize($botFilePath);
                $lastModified = date('Y-m-d H:i:s', filemtime($botFilePath));
                echo "<div class='file-info'>";
                echo "<h3>📄 اطلاعات فایل bot.php:</h3>";
                echo "<p><strong>حجم فایل:</strong> " . number_format($fileSize) . " بایت</p>";
                echo "<p><strong>آخرین تغییر:</strong> $lastModified</p>";
                echo "<p><strong>مسیر فایل:</strong> " . realpath($botFilePath) . "</p>";
                echo "</div>";
            }
            
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $newComment = $_POST['new_comment'] ?? '';

                if (empty($newComment)) {
                    echo "<div class='result error'>❌ لطفاً متن جدید کامنت را وارد کنید!</div>";
                } else {
                    if (!file_exists($botFilePath)) {
                        echo "<div class='result error'>❌ فایل bot.php یافت نشد!</div>";
                    } else {
                        // خواندن محتوای فایل
                        $content = file_get_contents($botFilePath);

                        if ($content === false) {
                            echo "<div class='result error'>❌ خطا در خواندن فایل bot.php!</div>";
                        } else {
                            // ایجاد بکاپ
                            if (copy($botFilePath, $backupFilePath)) {
                                echo "<div class='result success'>✅ بکاپ فایل در $backupFilePath ایجاد شد.</div>";
                            }

                            // آنالیز و شمارش تمام انواع کامنت‌های PHP
                            $lines = explode("\n", $content);
                            $commentStats = [
                                'single_line_comments' => 0,    // کامنت‌های //
                                'hash_comments' => 0,           // کامنت‌های #
                                'multi_line_comments' => 0,     // کامنت‌های /* */
                                'persian_comments' => 0,        // کامنت‌های "لینک چسبون"
                                'total_replaced' => 0
                            ];

                            // شمارش کامنت‌های موجود
                            foreach ($lines as $line) {
                                // کامنت‌های //
                                if (preg_match('/\/\//', $line)) {
                                    $commentStats['single_line_comments']++;
                                }
                                // کامنت‌های #
                                if (preg_match('/^\s*#/', $line)) {
                                    $commentStats['hash_comments']++;
                                }
                                // کامنت‌های لینک چسبون
                                if (strpos($line, 'لینک چسبون') !== false) {
                                    $commentStats['persian_comments']++;
                                }
                            }

                            // شمارش کامنت‌های /* */
                            $commentStats['multi_line_comments'] = preg_match_all('/\/\*.*?\*\//s', $content);

                            $originalCommentCount = $commentStats['single_line_comments'] +
                                                  $commentStats['hash_comments'] +
                                                  $commentStats['multi_line_comments'] +
                                                  $commentStats['persian_comments'];

                            $newContent = $content;

                            // 1. جایگزینی کامنت‌های // (تک خطی)
                            $newContent = preg_replace('/\/\/(.*)$/m', $newComment . '$1', $newContent);

                            // 2. جایگزینی کامنت‌های # (تک خطی)
                            $newContent = preg_replace('/^\s*#(.*)$/m', $newComment . '$1', $newContent);

                            // 3. جایگزینی کامنت‌های /* */ (چند خطی)
                            $newContent = preg_replace('/\/\*(.*?)\*\//s', $newComment . '$1', $newContent);

                            // 4. جایگزینی کامنت‌های فارسی "لینک چسبون"
                            $newContent = str_replace('لینک چسبون', $newComment, $newContent);

                            // 5. تصحیح URL های خراب شده
                            $newContent = str_replace('https:' . $newComment, 'https://', $newContent);
                            $newContent = str_replace('php:' . $newComment, 'php://', $newContent);

                            // شمارش نهایی
                            $newLines = explode("\n", $newContent);
                            $finalCommentCount = 0;
                            foreach ($newLines as $line) {
                                if (preg_match('/\/\/|^\s*#|\/\*|\*\//', $line) || strpos($line, 'لینک چسبون') !== false) {
                                    $finalCommentCount++;
                                }
                            }
                            $totalReplaced = $originalCommentCount - $finalCommentCount;

                            // ذخیره فایل جدید
                            if (file_put_contents($botFilePath, $newContent) !== false) {
                                echo "<div class='result success'>";
                                echo "🎉 <strong>عملیات با موفقیت انجام شد!</strong><br><br>";
                                echo "📊 <strong>آمار کامنت‌های یافت شده:</strong><br>";
                                echo "• کامنت‌های // : {$commentStats['single_line_comments']}<br>";
                                echo "• کامنت‌های # : {$commentStats['hash_comments']}<br>";
                                echo "• کامنت‌های /* */ : {$commentStats['multi_line_comments']}<br>";
                                echo "• کامنت‌های فارسی : {$commentStats['persian_comments']}<br>";
                                echo "• <strong>مجموع:</strong> $originalCommentCount<br><br>";
                                echo "🔄 تعداد کامنت‌های تغییر یافته: $totalReplaced<br>";
                                echo "📝 متن جدید: \"$newComment\"<br>";
                                echo "💾 فایل bot.php به‌روزرسانی شد.<br>";
                                if ($finalCommentCount > 0) {
                                    echo "⚠️ $finalCommentCount کامنت ممکن است باقی مانده باشد";
                                }
                                echo "</div>";
                            } else {
                                echo "<div class='result error'>❌ خطا در ذخیره فایل جدید!</div>";
                            }
                        }
                    }
                }
            }
            ?>
            
            <div class="info">
                <h3>📋 راهنمای استفاده:</h3>
                <ul>
                    <li><strong>شناسایی هوشمند:</strong> این ابزار تمام انواع کامنت‌های "لینک چسبون" را پیدا می‌کند:</li>
                    <ul style="margin-top: 10px;">
                        <li>📝 کامنت‌های مستقل (خطوط جداگانه)</li>
                        <li>💬 کامنت‌های انتهای خط کد</li>
                        <li>🔗 کامنت‌های داخل URL ها (تبدیل به // می‌شوند)</li>
                    </ul>
                    <li><strong>جایگزینی هوشمند:</strong> متن شما جایگزین کامنت‌ها می‌شود</li>
                    <li><strong>بکاپ خودکار:</strong> قبل از تغییر، فایل اصلی بکاپ می‌شود</li>
                    <li><strong>گزارش کامل:</strong> تعداد دقیق کامنت‌های تغییر یافته نمایش داده می‌شود</li>
                    <li><strong>انعطاف‌پذیر:</strong> متن جدید می‌تواند هر چیزی باشد (فارسی، انگلیسی، نماد و...)</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="new_comment">🖊️ متن جدید برای جایگزینی کامنت‌ها:</label>
                    <input type="text" 
                           id="new_comment" 
                           name="new_comment" 
                           placeholder="مثال: // کامنت جدید"
                           value="<?php echo htmlspecialchars($_POST['new_comment'] ?? ''); ?>"
                           required>
                </div>
                
                <button type="submit" class="btn">
                    🔄 تغییر تمام کامنت‌ها
                </button>
            </form>
            
            <?php if (file_exists($botFilePath)): ?>
            <div class="info" style="margin-top: 20px;">
                <h3>📈 آمار فعلی فایل:</h3>
                <?php
                $currentContent = file_get_contents($botFilePath);
                $totalLines = substr_count($currentContent, "\n") + 1;

                // آنالیز تمام انواع کامنت‌های PHP
                $lines = explode("\n", $currentContent);
                $currentStats = [
                    'single_line' => 0,    // //
                    'hash' => 0,           // #
                    'multi_line' => 0,     // /* */
                    'persian' => 0         // لینک چسبون
                ];

                foreach ($lines as $line) {
                    // کامنت‌های //
                    if (preg_match('/\/\//', $line)) {
                        $currentStats['single_line']++;
                    }
                    // کامنت‌های #
                    if (preg_match('/^\s*#/', $line)) {
                        $currentStats['hash']++;
                    }
                    // کامنت‌های فارسی
                    if (strpos($line, 'لینک چسبون') !== false) {
                        $currentStats['persian']++;
                    }
                }

                // کامنت‌های /* */
                $currentStats['multi_line'] = preg_match_all('/\/\*.*?\*\//s', $currentContent);

                $totalComments = $currentStats['single_line'] + $currentStats['hash'] +
                               $currentStats['multi_line'] + $currentStats['persian'];
                ?>
                <p><strong>تعداد خطوط:</strong> <?php echo number_format($totalLines); ?></p>
                <p><strong>تعداد کل کامنت‌ها:</strong> <?php echo $totalComments; ?></p>
                <div style="margin-right: 20px; font-size: 0.9em; color: #666;">
                    <p>// کامنت‌های تک خطی: <?php echo $currentStats['single_line']; ?></p>
                    <p># کامنت‌های هش: <?php echo $currentStats['hash']; ?></p>
                    <p>/* */ کامنت‌های چند خطی: <?php echo $currentStats['multi_line']; ?></p>
                    <p>🔗 کامنت‌های فارسی: <?php echo $currentStats['persian']; ?></p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
